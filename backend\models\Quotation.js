const mongoose = require('mongoose');

const QuotationItemSchema = new mongoose.Schema({
  itemId: { type: mongoose.Schema.Types.ObjectId, ref: 'ItemService', required: true },
  quantity: { type: Number, required: true },
  unitPrice: { type: Number, required: true }
}, { _id: false });

const QuotationSchema = new mongoose.Schema({
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Client', required: true },
  quotationNumber: { type: String, required: true },
  dateIssued: { type: Date, required: true, default: Date.now },
  dueDate: { type: Date },
  items: [QuotationItemSchema],
  totalAmount: { type: Number },
  status: { type: String, enum: ['draft', 'sent', 'accepted', 'rejected'], default: 'draft' },
  notes: { type: String }
}, { timestamps: true });

module.exports = mongoose.model('Quotation', QuotationSchema); 