import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Button, TextInput, Snackbar } from 'react-native-paper';
import api from '../../utils/localApi';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';

interface Settings {
  companyName?: string;
  companyAddress?: string;
  companyEmail?: string;
  companyPhone?: string;
  companyVatNumber?: string;
  defaultCurrency?: string;
  taxRate?: number;
}

export default function SettingsScreen() {
  const [settings, setSettings] = useState<Settings>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const res = await api.get('/settings');
      setSettings(res.data || {});
    } catch (err) {
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleSave = async () => {
    setLoading(true);
    try {
      await api.put('/settings', settings);
      setSuccess('Settings updated successfully!');
    } catch (err) {
      setError('Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await AsyncStorage.removeItem('token');
      setSuccess('Logged out successfully!');
      // Reload the app to trigger authentication check
      setTimeout(() => {
        router.replace('/');
      }, 1000);
    } catch (err) {
      setError('Failed to logout');
    }
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text variant="titleLarge" style={{ marginBottom: 16 }}>Company Settings</Text>
      <TextInput
        label="Company Name"
        value={settings.companyName || ''}
        onChangeText={v => setSettings(s => ({ ...s, companyName: v }))}
        style={styles.input}
      />
      <TextInput
        label="Company Address"
        value={settings.companyAddress || ''}
        onChangeText={v => setSettings(s => ({ ...s, companyAddress: v }))}
        style={styles.input}
        multiline
      />
      <TextInput
        label="Company Email"
        value={settings.companyEmail || ''}
        onChangeText={v => setSettings(s => ({ ...s, companyEmail: v }))}
        style={styles.input}
        keyboardType="email-address"
      />
      <TextInput
        label="Company Phone"
        value={settings.companyPhone || ''}
        onChangeText={v => setSettings(s => ({ ...s, companyPhone: v }))}
        style={styles.input}
        keyboardType="phone-pad"
      />
      <TextInput
        label="Company VAT Number"
        value={settings.companyVatNumber || ''}
        onChangeText={v => setSettings(s => ({ ...s, companyVatNumber: v }))}
        style={styles.input}
        placeholder="e.g., GB123456789, DE123456789"
      />
      <TextInput
        label="Default Currency"
        value={settings.defaultCurrency || 'USD'}
        onChangeText={v => setSettings(s => ({ ...s, defaultCurrency: v }))}
        style={styles.input}
      />
      <TextInput
        label="Tax Rate (%)"
        value={settings.taxRate?.toString() || '0'}
        onChangeText={v => setSettings(s => ({ ...s, taxRate: Number(v) }))}
        style={styles.input}
        keyboardType="numeric"
      />
      <Button mode="contained" onPress={handleSave} loading={loading} style={{ marginTop: 16 }}>
        Save Settings
      </Button>
      <Button mode="outlined" onPress={handleLogout} style={{ marginTop: 32, backgroundColor: '#ffebee' }}>
        Logout
      </Button>
      <Snackbar visible={!!error} onDismiss={() => setError('')} duration={3000} style={{ backgroundColor: 'red' }}>{error}</Snackbar>
      <Snackbar visible={!!success} onDismiss={() => setSuccess('')} duration={3000} style={{ backgroundColor: 'green' }}>{success}</Snackbar>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#fff',
  },
  input: {
    width: 300,
    marginBottom: 16,
  },
}); 