import React, { useEffect, useState } from 'react';
import { View, FlatList, StyleSheet, ScrollView, Platform } from 'react-native';
import { Text, Button, FAB, Dialog, Portal, TextInput, List, IconButton, Snackbar } from 'react-native-paper';
import { Picker } from '@react-native-picker/picker';
import api from '../../utils/api';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';

interface Client {
  _id?: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  taxId: string;
}

interface ItemService {
  _id?: string;
  name: string;
  description?: string;
  unitPrice: number;
  type: 'item' | 'service';
}

// For form data (when creating/editing)
interface QuotationItemForm {
  itemId: string;
  quantity: number;
  unitPrice: number;
}

interface QuotationForm {
  _id?: string;
  clientId: string;
  quotationNumber: string;
  dateIssued: string;
  dueDate?: string;
  items: QuotationItemForm[];
  totalAmount?: number;
  status?: string;
  notes?: string;
}

// For populated data (when displaying/printing)
interface QuotationItem {
  itemId: string | ItemService;
  quantity: number;
  unitPrice: number;
}

interface Quotation {
  _id?: string;
  clientId: string | Client;
  quotationNumber: string;
  dateIssued: string;
  dueDate?: string;
  items: QuotationItem[];
  totalAmount?: number;
  status?: string;
  notes?: string;
}

export default function QuotationsScreen() {
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDialog, setShowDialog] = useState(false);
  const [editingQuotation, setEditingQuotation] = useState<Quotation | null>(null);
  const [form, setForm] = useState<QuotationForm>({ clientId: '', quotationNumber: '', dateIssued: '', items: [{ itemId: '', quantity: 1, unitPrice: 0 }] });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [clients, setClients] = useState<Client[]>([]);
  const [items, setItems] = useState<ItemService[]>([]);
  const [search, setSearch] = useState('');

  const fetchQuotations = async () => {
    setLoading(true);
    try {
      const res = await api.get<Quotation[]>('/quotations');
      setQuotations(res.data);
    } catch (err) {
      setError('Failed to load quotations');
    } finally {
      setLoading(false);
    }
  };

  const fetchClientsAndItems = async () => {
    try {
      const [clientsRes, itemsRes] = await Promise.all([
        api.get('/clients'),
        api.get('/itemservices'),
      ]);
      setClients(clientsRes.data);
      setItems(itemsRes.data);
    } catch (err) {
      setError('Failed to load clients/items');
    }
  };

  useEffect(() => {
    fetchQuotations();
    fetchClientsAndItems();
  }, []);

  const openDialog = (quotation: Quotation | null = null) => {
    setEditingQuotation(quotation);
    if (quotation) {
      // Convert populated quotation to form data
      const formData: QuotationForm = {
        ...quotation,
        clientId: typeof quotation.clientId === 'object' ? quotation.clientId._id || '' : quotation.clientId,
        dateIssued: quotation.dateIssued?.slice(0, 10) || '',
        items: quotation.items.map(item => ({
          itemId: typeof item.itemId === 'object' ? item.itemId._id || '' : item.itemId,
          quantity: item.quantity,
          unitPrice: item.unitPrice
        }))
      };
      setForm(formData);
    } else {
      setForm({ clientId: '', quotationNumber: '', dateIssued: '', items: [{ itemId: '', quantity: 1, unitPrice: 0 }] });
    }
    setShowDialog(true);
  };

  const closeDialog = () => {
    setShowDialog(false);
    setEditingQuotation(null);
    setForm({ clientId: '', quotationNumber: '', dateIssued: '', items: [{ itemId: '', quantity: 1, unitPrice: 0 }] });
  };

  const handleSave = async () => {
    // Comprehensive validation
    if (!form.clientId) {
      setError('Please select a client.');
      return;
    }
    
    if (!form.items || form.items.length === 0) {
      setError('Please add at least one item.');
      return;
    }
    
    // Validate each item
    for (let i = 0; i < form.items.length; i++) {
      const item = form.items[i];
      if (!item.itemId) {
        setError(`Please select an item for row ${i + 1}.`);
        return;
      }
      if (!item.quantity || item.quantity <= 0) {
        setError(`Please enter a valid quantity for row ${i + 1}.`);
        return;
      }
      if (item.unitPrice === undefined || item.unitPrice < 0) {
        setError(`Please enter a valid unit price for row ${i + 1}.`);
        return;
      }
    }
    
    // Validate due date if provided
    if (form.dueDate) {
      const dueDate = new Date(form.dueDate);
      const today = new Date();
      if (isNaN(dueDate.getTime())) {
        setError('Please enter a valid due date (YYYY-MM-DD).');
        return;
      }
      if (dueDate < today) {
        setError('Due date cannot be in the past.');
        return;
      }
    }
    
    try {
      if (editingQuotation && editingQuotation._id) {
        await api.put(`/quotations/${editingQuotation._id}`, form);
        setSuccess('Quotation updated successfully!');
      } else {
        // Remove dateIssued from form on creation
        const { dateIssued, ...formWithoutDate } = form;
        await api.post('/quotations', formWithoutDate);
        setSuccess('Quotation created successfully!');
      }
      closeDialog();
      fetchQuotations();
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to save quotation. Please try again.';
      setError(errorMessage);
      console.error('Error saving quotation:', err);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await api.delete(`/quotations/${id}`);
      setSuccess('Quotation deleted');
      fetchQuotations();
    } catch (err) {
      setError('Failed to delete quotation');
    }
  };

  // For simplicity, add one item per quotation in this UI (can be improved later)
  const handleItemChange = (field: keyof QuotationItem, value: any, index: number) => {
    setForm(f => {
      const newForm = {
        ...f,
        items: f.items.map((item, i) => (i === index ? { ...item, [field]: value } : item)),
      };
      // Calculate total
      newForm.totalAmount = newForm.items.reduce((total, item) => {
        const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
        return total + itemTotal;
      }, 0);
      return newForm;
    });
  };

  const handleAddItem = () => {
    setForm(f => {
      const newForm = {
        ...f,
        items: [...f.items, { itemId: '', quantity: 1, unitPrice: 0 }],
      };
      // Calculate total
      newForm.totalAmount = newForm.items.reduce((total, item) => {
        const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
        return total + itemTotal;
      }, 0);
      return newForm;
    });
  };

  const handleRemoveItem = (index: number) => {
    setForm(f => {
      const newForm = {
        ...f,
        items: f.items.filter((_, i) => i !== index),
      };
      // Calculate total
      newForm.totalAmount = newForm.items.reduce((total, item) => {
        const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
        return total + itemTotal;
      }, 0);
      return newForm;
    });
  };

  const filteredQuotations = quotations.filter(q => {
    const clientName = typeof q.clientId === 'object'
      ? q.clientId.name
      : (clients.find(c => c._id === q.clientId)?.name || '');
    return (
      q.quotationNumber.toLowerCase().includes(search.toLowerCase()) ||
      clientName.toLowerCase().includes(search.toLowerCase()) ||
      (q.status && q.status.toLowerCase().includes(search.toLowerCase()))
    );
  });

  const exportQuotationToPDF = async (quotation: Quotation) => {
    // Handle both populated and non-populated client data
    const client = typeof quotation.clientId === 'object'
      ? quotation.clientId
      : clients.find(c => c._id === quotation.clientId);

    // Calculate responsive scaling based on content
    const itemCount = quotation.items.length;
    const hasNotes = quotation.notes && quotation.notes.trim().length > 0;

    // Determine scale factor based on content density
    let scaleFactor = 1.0;
    let baseFontSize = 12;
    let headerPadding = 18;
    let contentPadding = 16;
    let tablePadding = 6;
    let marginSize = 12;

    if (itemCount > 8) {
      scaleFactor = 0.75;
      baseFontSize = 10;
      headerPadding = 12;
      contentPadding = 10;
      tablePadding = 4;
      marginSize = 8;
    } else if (itemCount > 5) {
      scaleFactor = 0.85;
      baseFontSize = 11;
      headerPadding = 14;
      contentPadding = 12;
      tablePadding = 5;
      marginSize = 10;
    }

    // Adjust for additional content
    if (hasNotes) scaleFactor *= 0.95;

    // Fetch company settings for the PDF
    let companyInfo = {
      companyName: 'Your Company Name',
      companyAddress: 'Your Company Address',
      companyEmail: '<EMAIL>',
      companyPhone: '+****************'
    };
    
    try {
      const settingsRes = await api.get('/settings');
      if (settingsRes.data) {
        companyInfo = {
          companyName: settingsRes.data.companyName || companyInfo.companyName,
          companyAddress: settingsRes.data.companyAddress || companyInfo.companyAddress,
          companyEmail: settingsRes.data.companyEmail || companyInfo.companyEmail,
          companyPhone: settingsRes.data.companyPhone || companyInfo.companyPhone
        };
      }
    } catch (err) {
      console.log('Could not fetch company settings, using default values');
    }
    
    const currentDate = new Date().toLocaleDateString();
    const subtotal = quotation.totalAmount || 0;
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>Quotation #${quotation.quotationNumber}</title>
          <style>
            @page {
              size: A4;
              margin: ${marginSize}mm ${marginSize}mm ${marginSize}mm ${marginSize}mm;
            }
            html, body {
              width: 210mm;
              height: 297mm;
              margin: 0;
              padding: 0;
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              font-size: ${baseFontSize}px;
              color: #333;
              background: #fff;
              box-sizing: border-box;
              transform: scale(${scaleFactor});
              transform-origin: top left;
            }
            .container {
              max-width: ${186 * scaleFactor}mm;
              margin: 0 auto;
              background: white;
              border-radius: ${6 * scaleFactor}px;
              overflow: hidden;
              box-shadow: none;
              padding: 0;
              height: fit-content;
              max-height: ${270 * scaleFactor}mm;
            }
            .header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: ${headerPadding}px ${marginSize}px;
              text-align: center;
            }
            .header h1 {
              font-size: ${baseFontSize * 1.25}px;
              font-weight: 400;
              margin-bottom: ${4 * scaleFactor}px;
            }
            .quote-number {
              font-size: ${baseFontSize * 1.1}px;
              font-weight: 600;
              background: rgba(255,255,255,0.18);
              padding: ${4 * scaleFactor}px ${12 * scaleFactor}px;
              border-radius: ${16 * scaleFactor}px;
              display: inline-block;
              margin-top: ${4 * scaleFactor}px;
            }
            .content {
              padding: ${contentPadding}px ${marginSize}px 0 ${marginSize}px;
            }
            .company-client-section {
              display: flex;
              justify-content: space-between;
              margin-bottom: ${16 * scaleFactor}px;
              gap: ${12 * scaleFactor}px;
            }
            .company-info, .client-info {
              flex: 1;
              padding: ${10 * scaleFactor}px;
              border-radius: ${4 * scaleFactor}px;
              font-size: ${baseFontSize * 0.92}px;
            }
            .company-info {
              background: #f5f7fa;
              border-left: ${3 * scaleFactor}px solid #667eea;
            }
            .client-info {
              background: #ffecd2;
              border-left: ${3 * scaleFactor}px solid #ff9a56;
            }
            .section-title {
              font-size: ${baseFontSize * 0.95}px;
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: ${6 * scaleFactor}px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
            .info-line {
              margin-bottom: ${3 * scaleFactor}px;
              font-size: ${baseFontSize * 0.85}px;
            }
            .details-section {
              background: #f8f9fa;
              padding: ${10 * scaleFactor}px;
              border-radius: ${4 * scaleFactor}px;
              margin-bottom: ${10 * scaleFactor}px;
              border: 1px solid #e9ecef;
            }
            .details-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(${90 * scaleFactor}px, 1fr));
              gap: ${8 * scaleFactor}px;
            }
            .detail-item {
              background: white;
              padding: ${6 * scaleFactor}px;
              border-radius: ${3 * scaleFactor}px;
              border-left: ${2 * scaleFactor}px solid #667eea;
            }
            .detail-label {
              font-size: ${baseFontSize * 0.8}px;
              color: #6c757d;
              text-transform: uppercase;
              margin-bottom: ${2 * scaleFactor}px;
            }
            .detail-value {
              font-size: ${baseFontSize}px;
              font-weight: 600;
              color: #2c3e50;
            }
            .status-badge {
              padding: 2px 8px;
              border-radius: 10px;
              font-size: 0.8em;
              font-weight: 600;
              text-transform: uppercase;
              display: inline-block;
            }
            .status-draft { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
            .status-sent { background: #d1ecf1; color: #0c5460; border: 1px solid #74b9ff; }
            .status-accepted { background: #d4edda; color: #155724; border: 1px solid #00b894; }
            .status-rejected { background: #f8d7da; color: #721c24; border: 1px solid #e17055; }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              margin: ${10 * scaleFactor}px 0 0 0;
              background: white;
              border-radius: ${4 * scaleFactor}px;
              font-size: ${baseFontSize * 0.92}px;
            }
            .items-table thead {
              background: #667eea;
              color: white;
            }
            .items-table th, .items-table td {
              padding: ${tablePadding}px ${4 * scaleFactor}px;
              border-bottom: 1px solid #e9ecef;
              text-align: left;
            }
            .items-table th {
              font-size: ${baseFontSize * 0.85}px;
              font-weight: 600;
              text-transform: uppercase;
            }
            .items-table td {
              font-size: ${baseFontSize * 0.9}px;
            }
            .items-table tbody tr:last-child td {
              border-bottom: none;
            }
            .item-name {
              font-weight: 600;
              color: #2c3e50;
            }
            .item-type {
              font-size: ${baseFontSize * 0.75}px;
              color: #6c757d;
              font-style: italic;
            }
            .item-description {
              font-size: ${baseFontSize * 0.85}px;
              color: #6c757d;
              font-style: italic;
            }
            .quantity, .price {
              text-align: right;
            }
            .total-row {
              background: #f8f9fa;
              font-weight: 700;
              font-size: ${baseFontSize}px;
            }
            .total-row td {
              padding: ${8 * scaleFactor}px ${4 * scaleFactor}px;
              border-top: ${2 * scaleFactor}px solid #667eea;
            }
            .total-amount {
              color: #28a745;
              font-size: ${baseFontSize * 1.1}px;
            }
            .notes-section {
              background: #fff8e1;
              border: 1px solid #ffecb3;
              border-left: ${3 * scaleFactor}px solid #ffc107;
              padding: ${8 * scaleFactor}px;
              border-radius: ${4 * scaleFactor}px;
              margin: ${10 * scaleFactor}px 0;
              font-size: ${baseFontSize * 0.9}px;
            }
            .notes-title {
              color: #f57c00;
              font-size: ${baseFontSize}px;
              font-weight: 600;
              margin-bottom: ${6 * scaleFactor}px;
              text-transform: uppercase;
            }
            .footer {
              background: #2c3e50;
              color: white;
              padding: ${10 * scaleFactor}px;
              text-align: center;
              margin-top: ${10 * scaleFactor}px;
              font-size: ${baseFontSize * 0.9}px;
            }
            .footer-content {
              max-width: ${600 * scaleFactor}px;
              margin: 0 auto;
            }
            @media print {
              html, body {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
                font-size: ${baseFontSize}px;
                transform: scale(${scaleFactor});
                transform-origin: top left;
              }
              .container {
                max-width: ${186 * scaleFactor}mm;
                box-shadow: none;
                border-radius: 0;
                padding: 0;
                height: fit-content;
                max-height: ${270 * scaleFactor}mm;
              }
              .header, .footer {
                padding: ${8 * scaleFactor}px 0;
              }
              .content {
                padding: ${8 * scaleFactor}px 0 0 0;
              }
              .items-table th, .items-table td {
                padding: ${4 * scaleFactor}px ${2 * scaleFactor}px;
              }
              .notes-section {
                padding: ${4 * scaleFactor}px;
              }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="header-content">
                <h1>QUOTATION</h1>
                <div class="quote-number">#${quotation.quotationNumber}</div>
              </div>
            </div>
            <div class="content">
              <div class="company-client-section">
                <div class="company-info">
                  <div class="section-title">From</div>
                  <div class="info-line"><strong>${companyInfo.companyName}</strong></div>
                  
                  ${companyInfo.companyEmail ? `<div class="info-line">📧 ${companyInfo.companyEmail}</div>` : ''}
                  ${companyInfo.companyPhone ? `<div class="info-line">📞 ${companyInfo.companyPhone}</div>` : ''}
                </div>
                <div class="client-info">
                  <div class="section-title">To</div>
                  <div class="info-line"><strong>${client?.name || 'Unknown Client'}</strong></div>
                  ${client?.email ? `<div class="info-line">📧 ${client.email}</div>` : ''}
                  ${client?.phone ? `<div class="info-line">📞 ${client.phone}</div>` : ''}
                  ${client?.address ? `<div class="info-line">📍 ${client.address}</div>` : ''}
                  ${client?.taxId ? `<div class="info-line">🏢 Tax ID: ${client.taxId}</div>` : ''}
                </div>
              </div>
              <div class="details-section">
                <div class="details-grid">
                  <div class="detail-item">
                    <div class="detail-label">Date Issued</div>
                    <div class="detail-value">${quotation.dateIssued?.slice(0, 10) || currentDate}</div>
                  </div>
                  ${quotation.dueDate ? `
                    <div class="detail-item">
                      <div class="detail-label">Valid Until</div>
                      <div class="detail-value">${quotation.dueDate.slice(0, 10)}</div>
                    </div>
                  ` : ''}
                  <div class="detail-item">
                    <div class="detail-label">Status</div>
                    <div class="detail-value">
                      <span class="status-badge status-${quotation.status || 'draft'}">${(quotation.status || 'draft').toUpperCase()}</span>
                    </div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">Quote Total</div>
                    <div class="detail-value total-amount">€${subtotal.toFixed(2)}</div>
                  </div>
                </div>
              </div>
              <table class="items-table">
                <thead>
                  <tr>
                    <th style="width: 22%;">Item/Service</th>
                    <th style="width: 32%;">Description</th>
                    <th style="width: 8%;">Qty</th>
                    <th style="width: 14%;">Unit Price</th>
                    <th style="width: 14%;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  ${quotation.items.map(item => {
                    // Handle both populated and non-populated item data
                    const itemObj = typeof item.itemId === 'object'
                      ? item.itemId
                      : items.find(i => i._id === item.itemId);
                    const itemTotal = (item.unitPrice || 0) * (item.quantity || 0);
                    return `
                      <tr>
                        <td>
                          <div class="item-name">${itemObj?.name || 'Unknown Item'}</div>
                          <div class="item-type">${itemObj?.type ? `(${itemObj.type.charAt(0).toUpperCase() + itemObj.type.slice(1)})` : ''}</div>
                        </td>
                        <td>
                          <div class="item-description">${itemObj?.description || 'No description available'}</div>
                        </td>
                        <td class="quantity">${item.quantity}</td>
                        <td class="price">€${item.unitPrice.toFixed(2)}</td>
                        <td class="price">€${itemTotal.toFixed(2)}</td>
                      </tr>
                    `;
                  }).join('')}
                </tbody>
                <tfoot>
                  <tr class="total-row">
                    <td colspan="4"><strong>TOTAL AMOUNT</strong></td>
                    <td class="price total-amount"><strong>€${subtotal.toFixed(2)}</strong></td>
                  </tr>
                </tfoot>
              </table>
              ${quotation.notes ? `
                <div class="notes-section">
                  <div class="notes-title">📝 Additional Notes</div>
                  <div class="notes-content">${quotation.notes}</div>
                </div>
              ` : ''}
            </div>
            <div class="footer">
              <div class="footer-content">
                <h3>Thank You for Your Interest!</h3>
                <p>This quotation is valid until the specified date. Terms and conditions apply.</p>
                <p>Questions? Contact us at <span class="highlight">${companyInfo.companyEmail}</span> or <span class="highlight">${companyInfo.companyPhone}</span></p>
                <p>We look forward to doing business with you!</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
    
    try {
      // Check if we're on web platform
      const isWeb = Platform.OS === 'web';
      console.log('Platform.OS:', Platform.OS, 'isWeb:', isWeb);
      
      if (isWeb) {
        // For web platform, open in new window for printing
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          printWindow.document.write(html);
          printWindow.document.close();
          printWindow.focus();
          // Trigger print dialog
          setTimeout(() => {
            printWindow.print();
          }, 500);
          setSuccess('Quotation opened for printing in new window');
        } else {
          // Fallback: create blob and download
          const blob = new Blob([html], { type: 'text/html' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `Quotation-${quotation.quotationNumber}.html`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          setSuccess('Quotation HTML file downloaded');
        }
      } else {
        // For mobile platforms, use expo-print
        const result = await Print.printToFileAsync({
          html,
          base64: false,
          margins: {
            left: 0,
            top: 0,
            right: 0,
            bottom: 0
          }
        });
        
        if (!result || !result.uri) {
          throw new Error('PDF generation failed - no URI returned');
        }
        
        // Check if sharing is available
        const isAvailable = await Sharing.isAvailableAsync();
        if (isAvailable) {
          await Sharing.shareAsync(result.uri, {
            UTI: '.pdf',
            mimeType: 'application/pdf',
            dialogTitle: `Quotation #${quotation.quotationNumber}`
          });
        } else {
          setSuccess('PDF generated successfully');
        }
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      setError('Failed to generate PDF. Please try again.');
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <TextInput
        placeholder="Search quotations..."
        value={search}
        onChangeText={setSearch}
        style={{ margin: 12, marginBottom: 0 }}
      />
      <FlatList
        data={filteredQuotations}
        keyExtractor={(item) => item._id || ''}
        renderItem={({ item }) => (
          <List.Item
            title={`#${item.quotationNumber} - ${typeof item.clientId === 'object' ? item.clientId.name : (clients.find(c => c._id === item.clientId)?.name || item.clientId)}`}
            description={`Date: ${item.dateIssued?.slice(0, 10)} | Total: €${item.totalAmount?.toFixed(2) || '0.00'}`}
            right={() => (
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <IconButton icon="pencil" onPress={() => openDialog(item)} />
                <IconButton icon="delete" onPress={() => item._id && handleDelete(item._id)} />
                <IconButton icon="file-pdf-box" onPress={() => exportQuotationToPDF(item)} />
              </View>
            )}
          />
        )}
        refreshing={loading}
        onRefresh={fetchQuotations}
        ListEmptyComponent={<Text style={{ textAlign: 'center', marginTop: 32 }}>No quotations found.</Text>}
      />
      <FAB icon="plus" style={styles.fab} onPress={() => openDialog()} />
      <Portal>
        <Dialog visible={showDialog} onDismiss={closeDialog} style={{ maxHeight: '90%' }}>
          <Dialog.Title>{editingQuotation ? 'Edit Quotation' : 'Add Quotation'}</Dialog.Title>
          <Dialog.ScrollArea>
            <ScrollView>
              {editingQuotation && (
                <TextInput
                  label="Date Issued (YYYY-MM-DD)"
                  value={form.dateIssued}
                  onChangeText={v => setForm(f => ({ ...f, dateIssued: v }))}
                  style={styles.input}
                />
              )}
              <TextInput
                label="Due Date (YYYY-MM-DD)"
                value={form.dueDate || ''}
                onChangeText={v => setForm(f => ({ ...f, dueDate: v }))}
                style={styles.input}
                placeholder="Optional - Leave blank for no due date"
              />
              <View style={styles.pickerContainer}>
                <Text style={styles.pickerLabel}>Client *</Text>
                <Picker
                  selectedValue={form.clientId}
                  onValueChange={(v: string) => setForm(f => ({ ...f, clientId: v }))}
                  style={[styles.picker, !form.clientId && styles.pickerError]}
                >
                  <Picker.Item label="Select Client" value="" />
                  {clients.map(c => (
                    <Picker.Item key={c._id} label={c.name} value={c._id} />
                  ))}
                </Picker>
              </View>
              {form.items.map((item, idx) => (
                <View key={idx} style={styles.itemContainer}>
                  <Text style={styles.itemHeader}>Item {idx + 1}</Text>
                  <View style={styles.pickerContainer}>
                    <Text style={styles.pickerLabel}>Select Item/Service *</Text>
                    <Picker
                      selectedValue={item.itemId}
                      onValueChange={(v: string) => {
                        const selectedItem = items.find(i => i._id === v);
                        handleItemChange('itemId', v, idx);
                        if (selectedItem) {
                          handleItemChange('unitPrice', selectedItem.unitPrice, idx);
                        }
                      }}
                      style={[styles.picker, !item.itemId && styles.pickerError]}
                    >
                      <Picker.Item label="Select Item" value="" />
                      {items.map(i => (
                        <Picker.Item key={i._id} label={`${i.name} - €${i.unitPrice.toFixed(2)}`} value={i._id} />
                      ))}
                    </Picker>
                  </View>
                  <TextInput
                    label="Quantity"
                    value={item.quantity?.toString() || '1'}
                    onChangeText={v => handleItemChange('quantity', Number(v), idx)}
                    style={styles.input}
                    keyboardType="numeric"
                  />
                  <TextInput
                    label="Unit Price"
                    value={item.unitPrice?.toString() || '0'}
                    onChangeText={v => handleItemChange('unitPrice', Number(v), idx)}
                    style={styles.input}
                    keyboardType="numeric"
                  />
                  <Button onPress={() => handleRemoveItem(idx)} mode="outlined" style={{ marginTop: 4 }} disabled={form.items.length === 1}>
                    Remove Item
                  </Button>
                </View>
              ))}
              <Button onPress={handleAddItem} mode="contained" style={{ marginBottom: 8 }}>
                Add Item
              </Button>
              <Text style={{ textAlign: 'center', fontSize: 18, fontWeight: 'bold', marginBottom: 8 }}>
                Total: ${form.totalAmount?.toFixed(2) || '0.00'}
              </Text>
              <TextInput
                label="Notes"
                value={form.notes || ''}
                onChangeText={v => setForm(f => ({ ...f, notes: v }))}
                style={styles.input}
                multiline
              />
            </ScrollView>
          </Dialog.ScrollArea>
          <Dialog.Actions>
            <Button onPress={closeDialog}>Cancel</Button>
            <Button onPress={handleSave}>{editingQuotation ? 'Update' : 'Add'}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
      <Snackbar visible={!!error} onDismiss={() => setError('')} duration={3000} style={{ backgroundColor: 'red' }}>{error}</Snackbar>
      <Snackbar visible={!!success} onDismiss={() => setSuccess('')} duration={3000} style={{ backgroundColor: 'green' }}>{success}</Snackbar>
    </View>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
  },
  input: {
    marginBottom: 8,
  },
  pickerContainer: {
    marginBottom: 8,
  },
  pickerLabel: {
    fontSize: 14,
    marginBottom: 4,
    color: '#333',
  },
  picker: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    backgroundColor: '#fff',
  },
  pickerError: {
    borderColor: 'red',
    borderWidth: 2,
  },
  itemContainer: {
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    marginBottom: 8,
    padding: 8,
  },
  itemHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#555',
  },
}); 