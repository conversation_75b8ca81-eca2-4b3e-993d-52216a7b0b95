import AsyncStorage from '@react-native-async-storage/async-storage';

class WebDatabaseService {
  constructor() {
    this.isInitialized = false;
    this.data = {
      settings: [],
      clients: [],
      itemservices: [],
      quotations: [],
      invoices: []
    };
  }

  async init() {
    if (this.isInitialized) return;
    
    try {
      await this.loadData();
      this.isInitialized = true;
      console.log('Web database initialized successfully');
    } catch (error) {
      console.error('Web database initialization failed:', error);
      throw error;
    }
  }

  async loadData() {
    try {
      const keys = ['settings', 'clients', 'itemservices', 'quotations', 'invoices'];
      for (const key of keys) {
        const stored = await AsyncStorage.getItem(`bookkeeping_${key}`);
        this.data[key] = stored ? JSON.parse(stored) : [];
      }
      
      // Initialize default settings if none exist
      if (this.data.settings.length === 0) {
        this.data.settings = [{
          _id: '1',
          companyName: 'Your Company Name',
          companyAddress: 'Your Company Address',
          companyEmail: '<EMAIL>',
          companyPhone: '+****************',
          companyVatNumber: '',
          defaultCurrency: 'USD',
          taxRate: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }];
        await this.saveData('settings');
      }
    } catch (error) {
      console.error('Error loading web data:', error);
    }
  }

  async saveData(key) {
    try {
      await AsyncStorage.setItem(`bookkeeping_${key}`, JSON.stringify(this.data[key]));
    } catch (error) {
      console.error('Error saving web data:', error);
    }
  }

  generateId(collection) {
    const items = this.data[collection];
    if (items.length === 0) return '1';
    const maxId = Math.max(...items.map(item => parseInt(item._id || '0')));
    return (maxId + 1).toString();
  }

  // Settings methods
  async getSettings() {
    await this.init();
    const settings = this.data.settings[0] || null;
    return { data: settings };
  }

  async updateSettings(settingsData) {
    await this.init();
    const updatedSettings = {
      _id: '1',
      ...settingsData,
      createdAt: this.data.settings[0]?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.data.settings[0] = updatedSettings;
    await this.saveData('settings');
    return { data: updatedSettings };
  }

  // Clients methods
  async getClients() {
    await this.init();
    const clients = [...this.data.clients].sort((a, b) => a.name.localeCompare(b.name));
    return { data: clients };
  }

  async createClient(clientData) {
    await this.init();
    const newClient = {
      _id: this.generateId('clients'),
      ...clientData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.data.clients.push(newClient);
    await this.saveData('clients');
    return { data: newClient };
  }

  async updateClient(id, clientData) {
    await this.init();
    const clientIndex = this.data.clients.findIndex(c => c._id === id);
    if (clientIndex !== -1) {
      this.data.clients[clientIndex] = {
        ...this.data.clients[clientIndex],
        ...clientData,
        updatedAt: new Date().toISOString()
      };
      await this.saveData('clients');
      return { data: this.data.clients[clientIndex] };
    }
    throw new Error('Client not found');
  }

  async deleteClient(id) {
    await this.init();
    this.data.clients = this.data.clients.filter(c => c._id !== id);
    await this.saveData('clients');
    return { data: { message: 'Client deleted successfully' } };
  }

  // Items/Services methods
  async getItemServices() {
    await this.init();
    const items = [...this.data.itemservices].sort((a, b) => a.name.localeCompare(b.name));
    return { data: items };
  }

  async createItemService(itemData) {
    await this.init();
    const newItem = {
      _id: this.generateId('itemservices'),
      ...itemData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.data.itemservices.push(newItem);
    await this.saveData('itemservices');
    return { data: newItem };
  }

  async updateItemService(id, itemData) {
    await this.init();
    const itemIndex = this.data.itemservices.findIndex(i => i._id === id);
    if (itemIndex !== -1) {
      this.data.itemservices[itemIndex] = {
        ...this.data.itemservices[itemIndex],
        ...itemData,
        updatedAt: new Date().toISOString()
      };
      await this.saveData('itemservices');
      return { data: this.data.itemservices[itemIndex] };
    }
    throw new Error('Item not found');
  }

  async deleteItemService(id) {
    await this.init();
    this.data.itemservices = this.data.itemservices.filter(i => i._id !== id);
    await this.saveData('itemservices');
    return { data: { message: 'Item deleted successfully' } };
  }

  // Helper method to populate quotation/invoice data
  populateData(item, type) {
    if (type === 'quotation' || type === 'invoice') {
      // Populate client data
      if (item.clientId && typeof item.clientId === 'string') {
        const client = this.data.clients.find(c => c._id === item.clientId);
        if (client) {
          item.clientId = client;
        }
      }

      // Populate item data in items array
      if (item.items && Array.isArray(item.items)) {
        item.items = item.items.map(itemEntry => {
          if (itemEntry.itemId && typeof itemEntry.itemId === 'string') {
            const itemService = this.data.itemservices.find(i => i._id === itemEntry.itemId);
            if (itemService) {
              return {
                ...itemEntry,
                itemId: itemService
              };
            }
          }
          return itemEntry;
        });
      }

      // For invoices, populate quotation data
      if (type === 'invoice' && item.quotationId && typeof item.quotationId === 'string') {
        const quotation = this.data.quotations.find(q => q._id === item.quotationId);
        if (quotation) {
          item.quotationId = {
            _id: quotation._id,
            quotationNumber: quotation.quotationNumber
          };
        }
      }
    }
    return item;
  }

  // Quotations methods
  async getQuotations() {
    await this.init();
    const quotations = this.data.quotations
      .map(q => this.populateData({ ...q }, 'quotation'))
      .sort((a, b) => new Date(b.dateIssued) - new Date(a.dateIssued));
    return { data: quotations };
  }

  async createQuotation(quotationData) {
    await this.init();
    const newQuotation = {
      _id: this.generateId('quotations'),
      ...quotationData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.data.quotations.push(newQuotation);
    await this.saveData('quotations');
    return { data: this.populateData({ ...newQuotation }, 'quotation') };
  }

  async updateQuotation(id, quotationData) {
    await this.init();
    const quotationIndex = this.data.quotations.findIndex(q => q._id === id);
    if (quotationIndex !== -1) {
      this.data.quotations[quotationIndex] = {
        ...this.data.quotations[quotationIndex],
        ...quotationData,
        updatedAt: new Date().toISOString()
      };
      await this.saveData('quotations');
      return { data: this.populateData({ ...this.data.quotations[quotationIndex] }, 'quotation') };
    }
    throw new Error('Quotation not found');
  }

  async deleteQuotation(id) {
    await this.init();
    this.data.quotations = this.data.quotations.filter(q => q._id !== id);
    await this.saveData('quotations');
    return { data: { message: 'Quotation deleted successfully' } };
  }

  // Invoices methods
  async getInvoices() {
    await this.init();
    const invoices = this.data.invoices
      .map(i => this.populateData({ ...i }, 'invoice'))
      .sort((a, b) => new Date(b.dateIssued) - new Date(a.dateIssued));
    return { data: invoices };
  }

  async createInvoice(invoiceData) {
    await this.init();
    const newInvoice = {
      _id: this.generateId('invoices'),
      ...invoiceData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.data.invoices.push(newInvoice);
    await this.saveData('invoices');
    return { data: this.populateData({ ...newInvoice }, 'invoice') };
  }

  async updateInvoice(id, invoiceData) {
    await this.init();
    const invoiceIndex = this.data.invoices.findIndex(i => i._id === id);
    if (invoiceIndex !== -1) {
      this.data.invoices[invoiceIndex] = {
        ...this.data.invoices[invoiceIndex],
        ...invoiceData,
        updatedAt: new Date().toISOString()
      };
      await this.saveData('invoices');
      return { data: this.populateData({ ...this.data.invoices[invoiceIndex] }, 'invoice') };
    }
    throw new Error('Invoice not found');
  }

  async deleteInvoice(id) {
    await this.init();
    this.data.invoices = this.data.invoices.filter(i => i._id !== id);
    await this.saveData('invoices');
    return { data: { message: 'Invoice deleted successfully' } };
  }
}

// Create singleton instance
const webDatabaseService = new WebDatabaseService();
export default webDatabaseService;
