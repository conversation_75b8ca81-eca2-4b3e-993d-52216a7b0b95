import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { TextInput, Button, Text, Snackbar, Switch } from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../utils/localApi';

type AuthScreenProps = {
  navigation?: any;
  onAuthSuccess?: () => void;
};

export default function AuthScreen({ navigation, onAuthSuccess }: AuthScreenProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isRegister, setIsRegister] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleAuth = async () => {
    setLoading(true);
    setError('');
    setSuccess('');
    try {
      if (isRegister) {
        await api.register({ email, password });
        setSuccess('Registration successful! You can now log in.');
        setIsRegister(false);
      } else {
        const res = await api.login({ email, password });
        await AsyncStorage.setItem('token', res.data.token);
        setSuccess('Login successful!');
        if (onAuthSuccess) onAuthSuccess();
      }
    } catch (err) {
      setError((err as any).response?.data?.error || 'Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text variant="titleLarge" style={{ marginBottom: 16 }}>
        {isRegister ? 'Register' : 'Login'}
      </Text>
      <TextInput
        label="Email"
        value={email}
        onChangeText={setEmail}
        autoCapitalize="none"
        keyboardType="email-address"
        style={styles.input}
      />
      <TextInput
        label="Password"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
        style={styles.input}
      />
      <Button mode="contained" onPress={handleAuth} loading={loading} style={styles.button}>
        {isRegister ? 'Register' : 'Login'}
      </Button>
      <View style={styles.switchRow}>
        <Text>{isRegister ? 'Already have an account?' : "Don't have an account?"}</Text>
        <Switch value={isRegister} onValueChange={setIsRegister} />
      </View>
      <Snackbar
        visible={!!error}
        onDismiss={() => setError('')}
        duration={3000}
        style={{ backgroundColor: 'red' }}>
        {error}
      </Snackbar>
      <Snackbar
        visible={!!success}
        onDismiss={() => setSuccess('')}
        duration={3000}
        style={{ backgroundColor: 'green' }}>
        {success}
      </Snackbar>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#fff',
  },
  input: {
    width: 300,
    marginBottom: 16,
  },
  button: {
    width: 300,
    marginBottom: 16,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 8,
  },
}); 