{"version": 3, "file": "NativeStatement.d.ts", "sourceRoot": "", "sources": ["../src/NativeStatement.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;CACjB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,MAAM,eAAe,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,OAAO,GAAG,UAAU,CAAC;AAC5E,MAAM,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,GAAG,eAAe,EAAE,CAAC;AACnF,MAAM,MAAM,wBAAwB,GAAG,eAAe,EAAE,CAAC;AAEzD,MAAM,MAAM,yBAAyB,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;AAC7F,MAAM,MAAM,oBAAoB,GAAG,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC9D,MAAM,MAAM,iBAAiB,GAAG,MAAM,EAAE,CAAC;AACzC,MAAM,MAAM,kBAAkB,GAAG,GAAG,EAAE,CAAC;AACvC,MAAM,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAEpC;;GAEG;AACH,MAAM,CAAC,OAAO,OAAO,eAAe;IAG3B,QAAQ,CACb,QAAQ,EAAE,iBAAiB,EAC3B,UAAU,EAAE,yBAAyB,EACrC,cAAc,EAAE,oBAAoB,EACpC,iBAAiB,EAAE,OAAO,GACzB,OAAO,CAAC,eAAe,GAAG;QAAE,cAAc,EAAE,kBAAkB,CAAA;KAAE,CAAC;IAC7D,SAAS,CAAC,QAAQ,EAAE,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,GAAG,IAAI,GAAG,SAAS,CAAC;IACtF,WAAW,CAAC,QAAQ,EAAE,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;IACvE,UAAU,CAAC,QAAQ,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IACtD,mBAAmB,IAAI,OAAO,CAAC,iBAAiB,CAAC;IACjD,aAAa,CAAC,QAAQ,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAMzD,OAAO,CACZ,QAAQ,EAAE,iBAAiB,EAC3B,UAAU,EAAE,yBAAyB,EACrC,cAAc,EAAE,oBAAoB,EACpC,iBAAiB,EAAE,OAAO,GACzB,eAAe,GAAG;QAAE,cAAc,EAAE,kBAAkB,CAAA;KAAE;IACpD,QAAQ,CAAC,QAAQ,EAAE,iBAAiB,GAAG,kBAAkB,GAAG,IAAI,GAAG,SAAS;IAC5E,UAAU,CAAC,QAAQ,EAAE,iBAAiB,GAAG,kBAAkB,EAAE;IAC7D,SAAS,CAAC,QAAQ,EAAE,iBAAiB,GAAG,IAAI;IAC5C,kBAAkB,IAAI,MAAM,EAAE;IAC9B,YAAY,CAAC,QAAQ,EAAE,iBAAiB,GAAG,IAAI;CAGvD"}