import React, { useEffect, useState } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { Text, Button, FAB, Dialog, Portal, TextInput, List, IconButton, Snackbar } from 'react-native-paper';
import api from '../../utils/localApi';

interface Client {
  _id?: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  taxId: string;
}

export default function ClientsScreen() {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDialog, setShowDialog] = useState(false);
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [form, setForm] = useState<Client>({ name: '', email: '', phone: '', address: '', taxId: '' });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [search, setSearch] = useState('');

  const fetchClients = async () => {
    setLoading(true);
    try {
      const res = await api.get('/clients');
      setClients(res.data);
    } catch (err) {
      setError('Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  const openDialog = (client: Client | null = null) => {
    setEditingClient(client);
    setForm(client ? { ...client } : { name: '', email: '', phone: '', address: '', taxId: '' });
    setShowDialog(true);
  };

  const closeDialog = () => {
    setShowDialog(false);
    setEditingClient(null);
    setForm({ name: '', email: '', phone: '', address: '', taxId: '' });
  };

  const handleSave = async () => {
    try {
      if (editingClient && editingClient._id) {
        await api.put(`/clients/${editingClient._id}`, form);
        setSuccess('Client updated');
      } else {
        await api.post('/clients', form);
        setSuccess('Client added');
      }
      closeDialog();
      fetchClients();
    } catch (err) {
      setError('Failed to save client');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await api.delete(`/clients/${id}`);
      setSuccess('Client deleted');
      fetchClients();
    } catch (err) {
      setError('Failed to delete client');
    }
  };

  const filteredClients = clients.filter(c =>
    c.name.toLowerCase().includes(search.toLowerCase()) ||
    (c.email && c.email.toLowerCase().includes(search.toLowerCase())) ||
    (c.phone && c.phone.toLowerCase().includes(search.toLowerCase()))
  );

  return (
    <View style={{ flex: 1 }}>
      <TextInput
        placeholder="Search clients..."
        value={search}
        onChangeText={setSearch}
        style={{ margin: 12, marginBottom: 0 }}
      />
      <FlatList
        data={filteredClients}
        keyExtractor={(item) => item._id || ''}
        renderItem={({ item }) => (
          <List.Item
            title={item.name}
            description={item.email}
            right={() => (
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <IconButton icon="pencil" onPress={() => openDialog(item)} />
                <IconButton icon="delete" onPress={() => item._id && handleDelete(item._id)} />
              </View>
            )}
          />
        )}
        refreshing={loading}
        onRefresh={fetchClients}
        ListEmptyComponent={<Text style={{ textAlign: 'center', marginTop: 32 }}>No clients found.</Text>}
      />
      <FAB icon="plus" style={styles.fab} onPress={() => openDialog()} />
      <Portal>
        <Dialog visible={showDialog} onDismiss={closeDialog}>
          <Dialog.Title>{editingClient ? 'Edit Client' : 'Add Client'}</Dialog.Title>
          <Dialog.Content>
            <TextInput label="Name" value={form.name} onChangeText={v => setForm(f => ({ ...f, name: v }))} style={styles.input} />
            <TextInput label="Email" value={form.email} onChangeText={v => setForm(f => ({ ...f, email: v }))} style={styles.input} />
            <TextInput label="Phone" value={form.phone} onChangeText={v => setForm(f => ({ ...f, phone: v }))} style={styles.input} />
            <TextInput label="Address" value={form.address} onChangeText={v => setForm(f => ({ ...f, address: v }))} style={styles.input} />
            <TextInput label="Tax ID" value={form.taxId} onChangeText={v => setForm(f => ({ ...f, taxId: v }))} style={styles.input} />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={closeDialog}>Cancel</Button>
            <Button onPress={handleSave}>{editingClient ? 'Update' : 'Add'}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
      <Snackbar visible={!!error} onDismiss={() => setError('')} duration={3000} style={{ backgroundColor: 'red' }}>{error}</Snackbar>
      <Snackbar visible={!!success} onDismiss={() => setSuccess('')} duration={3000} style={{ backgroundColor: 'green' }}>{success}</Snackbar>
    </View>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
  },
  input: {
    marginBottom: 8,
  },
}); 