import * as SQLite from 'expo-sqlite';

class DatabaseService {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  async init() {
    if (this.isInitialized) return;
    
    try {
      this.db = await SQLite.openDatabaseAsync('bookkeeping.db');
      await this.createTables();
      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        companyName TEXT,
        companyAddress TEXT,
        companyEmail TEXT,
        companyPhone TEXT,
        companyVatNumber TEXT,
        defaultCurrency TEXT DEFAULT 'USD',
        taxRate REAL DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Clients table
      `CREATE TABLE IF NOT EXISTS clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        taxId TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Items/Services table
      `CREATE TABLE IF NOT EXISTS itemservices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        unitPrice REAL NOT NULL,
        type TEXT CHECK(type IN ('item', 'service')) NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Quotations table
      `CREATE TABLE IF NOT EXISTS quotations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        clientId INTEGER NOT NULL,
        quotationNumber TEXT NOT NULL UNIQUE,
        dateIssued DATETIME NOT NULL,
        dueDate DATETIME,
        totalAmount REAL,
        status TEXT CHECK(status IN ('draft', 'sent', 'accepted', 'rejected')) DEFAULT 'draft',
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clientId) REFERENCES clients (id)
      )`,

      // Quotation items table
      `CREATE TABLE IF NOT EXISTS quotation_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quotationId INTEGER NOT NULL,
        itemId INTEGER NOT NULL,
        quantity REAL NOT NULL,
        unitPrice REAL NOT NULL,
        FOREIGN KEY (quotationId) REFERENCES quotations (id) ON DELETE CASCADE,
        FOREIGN KEY (itemId) REFERENCES itemservices (id)
      )`,

      // Invoices table
      `CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        clientId INTEGER NOT NULL,
        quotationId INTEGER,
        invoiceNumber TEXT NOT NULL UNIQUE,
        dateIssued DATETIME NOT NULL,
        dueDate DATETIME,
        totalAmount REAL,
        amountPaid REAL DEFAULT 0,
        balanceDue REAL,
        status TEXT CHECK(status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')) DEFAULT 'draft',
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clientId) REFERENCES clients (id),
        FOREIGN KEY (quotationId) REFERENCES quotations (id)
      )`,

      // Invoice items table
      `CREATE TABLE IF NOT EXISTS invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoiceId INTEGER NOT NULL,
        itemId INTEGER NOT NULL,
        quantity REAL NOT NULL,
        unitPrice REAL NOT NULL,
        FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (itemId) REFERENCES itemservices (id)
      )`
    ];

    for (const table of tables) {
      await this.db.execAsync(table);
    }

    // Insert default settings if none exist
    const settingsCount = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM settings');
    if (settingsCount.count === 0) {
      await this.db.runAsync(
        'INSERT INTO settings (companyName, companyAddress, companyEmail, companyPhone) VALUES (?, ?, ?, ?)',
        ['Your Company Name', 'Your Company Address', '<EMAIL>', '+****************']
      );
    }
  }

  // Helper method to convert database row to API-like response
  convertToApiFormat(row, idField = 'id') {
    if (!row) return null;
    const converted = { ...row };
    converted._id = row[idField];
    delete converted[idField];
    return converted;
  }

  // Helper method to convert array of rows
  convertArrayToApiFormat(rows, idField = 'id') {
    return rows.map(row => this.convertToApiFormat(row, idField));
  }

  // Settings methods
  async getSettings() {
    await this.init();
    const settings = await this.db.getFirstAsync('SELECT * FROM settings ORDER BY id DESC LIMIT 1');
    return { data: this.convertToApiFormat(settings) };
  }

  async updateSettings(settingsData) {
    await this.init();
    const { companyName, companyAddress, companyEmail, companyPhone, companyVatNumber, defaultCurrency, taxRate } = settingsData;
    
    // Check if settings exist
    const existing = await this.db.getFirstAsync('SELECT id FROM settings LIMIT 1');
    
    if (existing) {
      await this.db.runAsync(
        `UPDATE settings SET 
         companyName = ?, companyAddress = ?, companyEmail = ?, companyPhone = ?, 
         companyVatNumber = ?, defaultCurrency = ?, taxRate = ?, updatedAt = CURRENT_TIMESTAMP 
         WHERE id = ?`,
        [companyName, companyAddress, companyEmail, companyPhone, companyVatNumber, defaultCurrency, taxRate, existing.id]
      );
    } else {
      await this.db.runAsync(
        `INSERT INTO settings (companyName, companyAddress, companyEmail, companyPhone, companyVatNumber, defaultCurrency, taxRate) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [companyName, companyAddress, companyEmail, companyPhone, companyVatNumber, defaultCurrency, taxRate]
      );
    }
    
    return await this.getSettings();
  }

  // Clients methods
  async getClients() {
    await this.init();
    const clients = await this.db.getAllAsync('SELECT * FROM clients ORDER BY name');
    return { data: this.convertArrayToApiFormat(clients) };
  }

  async createClient(clientData) {
    await this.init();
    const { name, email, phone, address, taxId } = clientData;
    const result = await this.db.runAsync(
      'INSERT INTO clients (name, email, phone, address, taxId) VALUES (?, ?, ?, ?, ?)',
      [name, email, phone, address, taxId]
    );
    
    const newClient = await this.db.getFirstAsync('SELECT * FROM clients WHERE id = ?', [result.lastInsertRowId]);
    return { data: this.convertToApiFormat(newClient) };
  }

  async updateClient(id, clientData) {
    await this.init();
    const { name, email, phone, address, taxId } = clientData;
    await this.db.runAsync(
      'UPDATE clients SET name = ?, email = ?, phone = ?, address = ?, taxId = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
      [name, email, phone, address, taxId, id]
    );
    
    const updatedClient = await this.db.getFirstAsync('SELECT * FROM clients WHERE id = ?', [id]);
    return { data: this.convertToApiFormat(updatedClient) };
  }

  async deleteClient(id) {
    await this.init();
    await this.db.runAsync('DELETE FROM clients WHERE id = ?', [id]);
    return { data: { message: 'Client deleted successfully' } };
  }

  // Items/Services methods
  async getItemServices() {
    await this.init();
    const items = await this.db.getAllAsync('SELECT * FROM itemservices ORDER BY name');
    return { data: this.convertArrayToApiFormat(items) };
  }

  async createItemService(itemData) {
    await this.init();
    const { name, description, unitPrice, type } = itemData;
    const result = await this.db.runAsync(
      'INSERT INTO itemservices (name, description, unitPrice, type) VALUES (?, ?, ?, ?)',
      [name, description, unitPrice, type]
    );
    
    const newItem = await this.db.getFirstAsync('SELECT * FROM itemservices WHERE id = ?', [result.lastInsertRowId]);
    return { data: this.convertToApiFormat(newItem) };
  }

  async updateItemService(id, itemData) {
    await this.init();
    const { name, description, unitPrice, type } = itemData;
    await this.db.runAsync(
      'UPDATE itemservices SET name = ?, description = ?, unitPrice = ?, type = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
      [name, description, unitPrice, type, id]
    );
    
    const updatedItem = await this.db.getFirstAsync('SELECT * FROM itemservices WHERE id = ?', [id]);
    return { data: this.convertToApiFormat(updatedItem) };
  }

  async deleteItemService(id) {
    await this.init();
    await this.db.runAsync('DELETE FROM itemservices WHERE id = ?', [id]);
    return { data: { message: 'Item deleted successfully' } };
  }

  // Quotations methods
  async getQuotations() {
    await this.init();
    const quotations = await this.db.getAllAsync(`
      SELECT q.*, c.name as clientName, c.email as clientEmail, c.phone as clientPhone,
             c.address as clientAddress, c.taxId as clientTaxId
      FROM quotations q
      LEFT JOIN clients c ON q.clientId = c.id
      ORDER BY q.dateIssued DESC
    `);

    // Get items for each quotation
    for (let quotation of quotations) {
      const items = await this.db.getAllAsync(`
        SELECT qi.quantity, qi.unitPrice, i.id as itemId, i.name, i.description, i.type
        FROM quotation_items qi
        JOIN itemservices i ON qi.itemId = i.id
        WHERE qi.quotationId = ?
      `, [quotation.id]);

      quotation.items = items.map(item => ({
        itemId: {
          _id: item.itemId,
          name: item.name,
          description: item.description,
          type: item.type
        },
        quantity: item.quantity,
        unitPrice: item.unitPrice
      }));

      // Add populated client data
      if (quotation.clientName) {
        quotation.clientId = {
          _id: quotation.clientId,
          name: quotation.clientName,
          email: quotation.clientEmail,
          phone: quotation.clientPhone,
          address: quotation.clientAddress,
          taxId: quotation.clientTaxId
        };
      }
    }

    return { data: this.convertArrayToApiFormat(quotations) };
  }

  async createQuotation(quotationData) {
    await this.init();
    const { clientId, quotationNumber, dateIssued, dueDate, items, totalAmount, status, notes } = quotationData;

    const result = await this.db.runAsync(
      'INSERT INTO quotations (clientId, quotationNumber, dateIssued, dueDate, totalAmount, status, notes) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [clientId, quotationNumber, dateIssued, dueDate, totalAmount, status || 'draft', notes]
    );

    const quotationId = result.lastInsertRowId;

    // Insert quotation items
    for (const item of items) {
      await this.db.runAsync(
        'INSERT INTO quotation_items (quotationId, itemId, quantity, unitPrice) VALUES (?, ?, ?, ?)',
        [quotationId, item.itemId, item.quantity, item.unitPrice]
      );
    }

    // Return the created quotation with populated data
    const quotations = await this.getQuotations();
    const newQuotation = quotations.data.find(q => q._id === quotationId);
    return { data: newQuotation };
  }

  async updateQuotation(id, quotationData) {
    await this.init();
    const { clientId, quotationNumber, dateIssued, dueDate, items, totalAmount, status, notes } = quotationData;

    await this.db.runAsync(
      'UPDATE quotations SET clientId = ?, quotationNumber = ?, dateIssued = ?, dueDate = ?, totalAmount = ?, status = ?, notes = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
      [clientId, quotationNumber, dateIssued, dueDate, totalAmount, status, notes, id]
    );

    // Delete existing items and insert new ones
    await this.db.runAsync('DELETE FROM quotation_items WHERE quotationId = ?', [id]);

    for (const item of items) {
      await this.db.runAsync(
        'INSERT INTO quotation_items (quotationId, itemId, quantity, unitPrice) VALUES (?, ?, ?, ?)',
        [id, item.itemId, item.quantity, item.unitPrice]
      );
    }

    // Return the updated quotation with populated data
    const quotations = await this.getQuotations();
    const updatedQuotation = quotations.data.find(q => q._id === parseInt(id));
    return { data: updatedQuotation };
  }

  async deleteQuotation(id) {
    await this.init();
    await this.db.runAsync('DELETE FROM quotation_items WHERE quotationId = ?', [id]);
    await this.db.runAsync('DELETE FROM quotations WHERE id = ?', [id]);
    return { data: { message: 'Quotation deleted successfully' } };
  }

  // Invoices methods
  async getInvoices() {
    await this.init();
    const invoices = await this.db.getAllAsync(`
      SELECT i.*, c.name as clientName, c.email as clientEmail, c.phone as clientPhone,
             c.address as clientAddress, c.taxId as clientTaxId,
             q.quotationNumber as quotationNumber
      FROM invoices i
      LEFT JOIN clients c ON i.clientId = c.id
      LEFT JOIN quotations q ON i.quotationId = q.id
      ORDER BY i.dateIssued DESC
    `);

    // Get items for each invoice
    for (let invoice of invoices) {
      const items = await this.db.getAllAsync(`
        SELECT ii.quantity, ii.unitPrice, i.id as itemId, i.name, i.description, i.type
        FROM invoice_items ii
        JOIN itemservices i ON ii.itemId = i.id
        WHERE ii.invoiceId = ?
      `, [invoice.id]);

      invoice.items = items.map(item => ({
        itemId: {
          _id: item.itemId,
          name: item.name,
          description: item.description,
          type: item.type
        },
        quantity: item.quantity,
        unitPrice: item.unitPrice
      }));

      // Add populated client data
      if (invoice.clientName) {
        invoice.clientId = {
          _id: invoice.clientId,
          name: invoice.clientName,
          email: invoice.clientEmail,
          phone: invoice.clientPhone,
          address: invoice.clientAddress,
          taxId: invoice.clientTaxId
        };
      }

      // Add populated quotation data if exists
      if (invoice.quotationId && invoice.quotationNumber) {
        invoice.quotationId = {
          _id: invoice.quotationId,
          quotationNumber: invoice.quotationNumber
        };
      }
    }

    return { data: this.convertArrayToApiFormat(invoices) };
  }

  async createInvoice(invoiceData) {
    await this.init();
    const { clientId, quotationId, invoiceNumber, dateIssued, dueDate, items, totalAmount, amountPaid, balanceDue, status, notes } = invoiceData;

    const result = await this.db.runAsync(
      'INSERT INTO invoices (clientId, quotationId, invoiceNumber, dateIssued, dueDate, totalAmount, amountPaid, balanceDue, status, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [clientId, quotationId, invoiceNumber, dateIssued, dueDate, totalAmount, amountPaid || 0, balanceDue, status || 'draft', notes]
    );

    const invoiceId = result.lastInsertRowId;

    // Insert invoice items
    for (const item of items) {
      await this.db.runAsync(
        'INSERT INTO invoice_items (invoiceId, itemId, quantity, unitPrice) VALUES (?, ?, ?, ?)',
        [invoiceId, item.itemId, item.quantity, item.unitPrice]
      );
    }

    // Return the created invoice with populated data
    const invoices = await this.getInvoices();
    const newInvoice = invoices.data.find(inv => inv._id === invoiceId);
    return { data: newInvoice };
  }

  async updateInvoice(id, invoiceData) {
    await this.init();
    const { clientId, quotationId, invoiceNumber, dateIssued, dueDate, items, totalAmount, amountPaid, balanceDue, status, notes } = invoiceData;

    await this.db.runAsync(
      'UPDATE invoices SET clientId = ?, quotationId = ?, invoiceNumber = ?, dateIssued = ?, dueDate = ?, totalAmount = ?, amountPaid = ?, balanceDue = ?, status = ?, notes = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
      [clientId, quotationId, invoiceNumber, dateIssued, dueDate, totalAmount, amountPaid, balanceDue, status, notes, id]
    );

    // Delete existing items and insert new ones
    await this.db.runAsync('DELETE FROM invoice_items WHERE invoiceId = ?', [id]);

    for (const item of items) {
      await this.db.runAsync(
        'INSERT INTO invoice_items (invoiceId, itemId, quantity, unitPrice) VALUES (?, ?, ?, ?)',
        [id, item.itemId, item.quantity, item.unitPrice]
      );
    }

    // Return the updated invoice with populated data
    const invoices = await this.getInvoices();
    const updatedInvoice = invoices.data.find(inv => inv._id === parseInt(id));
    return { data: updatedInvoice };
  }

  async deleteInvoice(id) {
    await this.init();
    await this.db.runAsync('DELETE FROM invoice_items WHERE invoiceId = ?', [id]);
    await this.db.runAsync('DELETE FROM invoices WHERE id = ?', [id]);
    return { data: { message: 'Invoice deleted successfully' } };
  }
}

// Create singleton instance
const databaseService = new DatabaseService();
export default databaseService;
