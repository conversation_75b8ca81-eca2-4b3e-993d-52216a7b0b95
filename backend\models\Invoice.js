const mongoose = require('mongoose');

const InvoiceItemSchema = new mongoose.Schema({
  itemId: { type: mongoose.Schema.Types.ObjectId, ref: 'ItemService', required: true },
  quantity: { type: Number, required: true },
  unitPrice: { type: Number, required: true }
}, { _id: false });

const InvoiceSchema = new mongoose.Schema({
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Client', required: true },
  quotationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Quotation' },
  invoiceNumber: { type: String, required: true },
  dateIssued: { type: Date, required: true, default: Date.now },
  dueDate: { type: Date },
  items: [InvoiceItemSchema],
  totalAmount: { type: Number },
  amountPaid: { type: Number, default: 0 },
  balanceDue: { type: Number },
  status: { type: String, enum: ['unpaid', 'partially_paid', 'paid', 'overdue'], default: 'unpaid' },
  notes: { type: String }
}, { timestamps: true });

module.exports = mongoose.model('Invoice', InvoiceSchema); 