{"version": 3, "file": "pathUtils.js", "sourceRoot": "", "sources": ["../src/pathUtils.ts"], "names": [], "mappings": "AAAA,OAAO,UAAU,MAAM,cAAc,CAAC;AAEtC;;;;GAIG;AACH,SAAS,kBAAkB,CAAC,SAA6B;IACvD,MAAM,iBAAiB,GAAG,SAAS,IAAI,UAAU,CAAC,wBAAwB,CAAC;IAC3E,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;IACpF,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAAC,YAAoB,EAAE,SAAkB;IACzE,IAAI,YAAY,KAAK,UAAU;QAAE,OAAO,YAAY,CAAC;IACrD,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAExD,SAAS,mBAAmB,CAAC,IAAY;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IACD,SAAS,kBAAkB,CAAC,IAAY;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,OAAO,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC;AACzF,CAAC", "sourcesContent": ["import ExpoSQLite from './ExpoSQLite';\n\n/**\n * Resolves the database directory from the given directory or the default directory.\n *\n * @hidden\n */\nfunction resolveDbDirectory(directory: string | undefined): string {\n  const resolvedDirectory = directory ?? ExpoSQLite.defaultDatabaseDirectory;\n  if (resolvedDirectory === null) {\n    throw new Error('Both provided directory and defaultDatabaseDirectory are null.');\n  }\n  return resolvedDirectory;\n}\n\n/**\n * Creates a normalized database path by combining the directory and database name.\n *\n * Ensures the directory does not end with a trailing slash and the database name\n * does not start with a leading slash, preventing redundant slashes in the final path.\n *\n * @hidden\n */\nexport function createDatabasePath(databaseName: string, directory?: string): string {\n  if (databaseName === ':memory:') return databaseName;\n  const resolvedDirectory = resolveDbDirectory(directory);\n\n  function removeTrailingSlash(path: string): string {\n    return path.replace(/\\/*$/, '');\n  }\n  function removeLeadingSlash(path: string): string {\n    return path.replace(/^\\/+/, '');\n  }\n\n  return `${removeTrailingSlash(resolvedDirectory)}/${removeLeadingSlash(databaseName)}`;\n}\n"]}