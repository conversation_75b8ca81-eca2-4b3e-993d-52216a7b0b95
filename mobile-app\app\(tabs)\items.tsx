import React, { useEffect, useState } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { Text, Button, FAB, Dialog, Portal, TextInput, List, IconButton, Snackbar } from 'react-native-paper';
import { Picker } from '@react-native-picker/picker';
import api from '../../utils/localApi';

interface ItemService {
  _id?: string;
  name: string;
  description?: string;
  unitPrice: number;
  type: 'item' | 'service';
}

export default function ItemsScreen() {
  const [items, setItems] = useState<ItemService[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDialog, setShowDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<ItemService | null>(null);
  const [form, setForm] = useState<ItemService>({ name: '', description: '', unitPrice: 0, type: 'item' });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [search, setSearch] = useState('');

  const fetchItems = async () => {
    setLoading(true);
    try {
      const res = await api.get('/itemservices');
      setItems(res.data);
    } catch (err) {
      setError('Failed to load items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  const openDialog = (item: ItemService | null = null) => {
    setEditingItem(item);
    setForm(item ? { ...item } : { name: '', description: '', unitPrice: 0, type: 'item' });
    setShowDialog(true);
  };

  const closeDialog = () => {
    setShowDialog(false);
    setEditingItem(null);
    setForm({ name: '', description: '', unitPrice: 0, type: 'item' });
  };

  const handleSave = async () => {
    // Validation
    if (!form.name || form.name.trim() === '') {
      setError('Item name is required.');
      return;
    }
    
    if (!form.type) {
      setError('Please select item or service type.');
      return;
    }
    
    if (form.unitPrice === undefined || form.unitPrice < 0) {
      setError('Please enter a valid unit price (must be 0 or greater).');
      return;
    }
    
    try {
      if (editingItem && editingItem._id) {
        await api.put(`/itemservices/${editingItem._id}`, form);
        setSuccess('Item updated successfully!');
      } else {
        await api.post('/itemservices', form);
        setSuccess('Item created successfully!');
      }
      closeDialog();
      fetchItems();
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to save item. Please try again.';
      setError(errorMessage);
      console.error('Error saving item:', err);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await api.delete(`/itemservices/${id}`);
      setSuccess('Item deleted');
      fetchItems();
    } catch (err) {
      setError('Failed to delete item');
    }
  };

  const filteredItems = items.filter(i =>
    i.name.toLowerCase().includes(search.toLowerCase()) ||
    (i.description && i.description.toLowerCase().includes(search.toLowerCase()))
  );

  return (
    <View style={{ flex: 1 }}>
      <TextInput
        placeholder="Search items..."
        value={search}
        onChangeText={setSearch}
        style={{ margin: 12, marginBottom: 0 }}
      />
      <FlatList
        data={filteredItems}
        keyExtractor={(item) => item._id || ''}
        renderItem={({ item }) => (
          <List.Item
            title={item.name}
            description={`€${item.unitPrice.toFixed(2)} | ${item.type} | ${item.description || ''}`}
            right={() => (
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <IconButton icon="pencil" onPress={() => openDialog(item)} />
                <IconButton icon="delete" onPress={() => item._id && handleDelete(item._id)} />
              </View>
            )}
          />
        )}
        refreshing={loading}
        onRefresh={fetchItems}
        ListEmptyComponent={<Text style={{ textAlign: 'center', marginTop: 32 }}>No items found.</Text>}
      />
      <FAB icon="plus" style={styles.fab} onPress={() => openDialog()} />
      <Portal>
        <Dialog visible={showDialog} onDismiss={closeDialog}>
          <Dialog.Title>{editingItem ? 'Edit Item' : 'Add Item'}</Dialog.Title>
          <Dialog.Content>
            <TextInput label="Name" value={form.name} onChangeText={v => setForm(f => ({ ...f, name: v }))} style={styles.input} />
            <TextInput label="Description" value={form.description || ''} onChangeText={v => setForm(f => ({ ...f, description: v }))} style={styles.input} />
            <TextInput label="Unit Price" value={form.unitPrice?.toString() || '0'} onChangeText={v => setForm(f => ({ ...f, unitPrice: Number(v) }))} style={styles.input} keyboardType="numeric" />
            <Picker
              selectedValue={form.type}
              onValueChange={(itemValue) => setForm(f => ({ ...f, type: itemValue as 'item' | 'service' }))}
              style={styles.picker}
            >
              <Picker.Item label="Select Type" value="" />
              <Picker.Item label="Item" value="item" />
              <Picker.Item label="Service" value="service" />
            </Picker>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={closeDialog}>Cancel</Button>
            <Button onPress={handleSave}>{editingItem ? 'Update' : 'Add'}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
      <Snackbar visible={!!error} onDismiss={() => setError('')} duration={3000} style={{ backgroundColor: 'red' }}>{error}</Snackbar>
      <Snackbar visible={!!success} onDismiss={() => setSuccess('')} duration={3000} style={{ backgroundColor: 'green' }}>{success}</Snackbar>
    </View>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
  },
  input: {
    marginBottom: 8,
  },
  picker: {
    marginTop: 8,
    marginBottom: 8,
  },
}); 