{"version": 3, "file": "NativeSession.js", "sourceRoot": "", "sources": ["../src/NativeSession.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * A type that represents a changeset.\n */\nexport type Changeset = Uint8Array;\n\nexport type SQLiteAnyDatabase = any;\n\nexport declare class NativeSession {\n  //#region Asynchronous API\n\n  public attachAsync(database: SQLiteAnyDatabase, table: string | null): Promise<void>;\n  public enableAsync(database: SQLiteAnyDatabase, enabled: boolean): Promise<void>;\n  public closeAsync(database: SQLiteAnyDatabase): Promise<void>;\n\n  public createChangesetAsync(database: SQLiteAnyDatabase): Promise<Changeset>;\n  public createInvertedChangesetAsync(database: SQLiteAnyDatabase): Promise<Changeset>;\n  public applyChangesetAsync(database: SQLiteAnyDatabase, changeset: Changeset): Promise<void>;\n  public invertChangesetAsync(\n    database: SQLiteAnyDatabase,\n    changeset: Changeset\n  ): Promise<Changeset>;\n\n  //#endregion\n\n  //#region Synchronous API\n\n  public attachSync(database: SQLiteAnyDatabase, table: string | null): void;\n  public enableSync(database: SQLiteAnyDatabase, enabled: boolean): void;\n  public closeSync(database: SQLiteAnyDatabase): void;\n\n  public createChangesetSync(database: SQLiteAnyDatabase): Changeset;\n  public createInvertedChangesetSync(database: SQLiteAnyDatabase): Changeset;\n  public applyChangesetSync(database: SQLiteAnyDatabase, changeset: Changeset): void;\n  public invertChangesetSync(database: SQLiteAnyDatabase, changeset: Changeset): Changeset;\n\n  //#endregion\n}\n"]}