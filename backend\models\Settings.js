const mongoose = require('mongoose');

const SettingsSchema = new mongoose.Schema({
  companyName: { type: String },
  companyAddress: { type: String },
  companyEmail: { type: String, match: /.+\@.+\..+/ },
  companyPhone: { type: String },
  defaultCurrency: { type: String, default: 'USD' },
  taxRate: { type: Number, default: 0 }
}, { timestamps: true });

module.exports = mongoose.model('Settings', SettingsSchema); 