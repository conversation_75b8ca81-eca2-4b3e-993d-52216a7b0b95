{"version": 3, "file": "NativeDatabase.d.ts", "sourceRoot": "", "sources": ["../src/NativeDatabase.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,OAAO,OAAO,cAAc;gBACrB,YAAY,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAAE,UAAU;IAInF,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC;IAC1B,oBAAoB,IAAI,OAAO,CAAC,OAAO,CAAC;IACxC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAC3B,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACxC,cAAc,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;IACzD,YAAY,CAAC,eAAe,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC;IACxF,kBAAkB,CAAC,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IAMxF,QAAQ,IAAI,IAAI;IAChB,mBAAmB,IAAI,OAAO;IAC9B,SAAS,IAAI,IAAI;IACjB,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAC9B,aAAa,CAAC,YAAY,EAAE,MAAM,GAAG,UAAU;IAC/C,WAAW,CAAC,eAAe,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,GAAG,eAAe;IAC9E,iBAAiB,CAAC,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,GAAG,aAAa;IAI9E,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;CACnC;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;;OAGG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B;;;OAGG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B;;;;OAIG;IACH,qCAAqC,CAAC,EAAE,OAAO,CAAC;IAEhD;;OAEG;IACH,aAAa,CAAC,EAAE;QACd,oCAAoC;QACpC,GAAG,EAAE,MAAM,CAAC;QAEZ,4CAA4C;QAC5C,SAAS,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;KACtB,CAAC;CACH;AAED,KAAK,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,EAAE,eAAe,CAAC,GAAG;IACrE,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B,CAAC;AAEF;;GAEG;AACH,wBAAgB,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,GAAG,oBAAoB,CAanF"}