import databaseService from './database';

// Local API service that mimics the HTTP API interface
// This allows us to replace the backend API calls with local database operations
// while keeping the same interface for the frontend components

class LocalApiService {
  // Settings endpoints
  async get(endpoint) {
    try {
      switch (endpoint) {
        case '/settings':
          return await databaseService.getSettings();
        
        case '/clients':
          return await databaseService.getClients();
        
        case '/itemservices':
          return await databaseService.getItemServices();
        
        case '/quotations':
          return await databaseService.getQuotations();
        
        case '/invoices':
          return await databaseService.getInvoices();
        
        default:
          throw new Error(`Unknown GET endpoint: ${endpoint}`);
      }
    } catch (error) {
      console.error('LocalAPI GET error:', error);
      throw this.formatError(error);
    }
  }

  async post(endpoint, data) {
    try {
      switch (endpoint) {
        case '/settings':
          return await databaseService.updateSettings(data);
        
        case '/clients':
          return await databaseService.createClient(data);
        
        case '/itemservices':
          return await databaseService.createItemService(data);
        
        case '/quotations':
          return await databaseService.createQuotation(data);
        
        case '/invoices':
          return await databaseService.createInvoice(data);
        
        default:
          throw new Error(`Unknown POST endpoint: ${endpoint}`);
      }
    } catch (error) {
      console.error('LocalAPI POST error:', error);
      throw this.formatError(error);
    }
  }

  async put(endpoint, data) {
    try {
      const parts = endpoint.split('/');
      const resource = parts[1];
      const id = parts[2];

      switch (resource) {
        case 'clients':
          return await databaseService.updateClient(id, data);
        
        case 'itemservices':
          return await databaseService.updateItemService(id, data);
        
        case 'quotations':
          return await databaseService.updateQuotation(id, data);
        
        case 'invoices':
          return await databaseService.updateInvoice(id, data);
        
        default:
          throw new Error(`Unknown PUT endpoint: ${endpoint}`);
      }
    } catch (error) {
      console.error('LocalAPI PUT error:', error);
      throw this.formatError(error);
    }
  }

  async delete(endpoint) {
    try {
      const parts = endpoint.split('/');
      const resource = parts[1];
      const id = parts[2];

      switch (resource) {
        case 'clients':
          return await databaseService.deleteClient(id);
        
        case 'itemservices':
          return await databaseService.deleteItemService(id);
        
        case 'quotations':
          return await databaseService.deleteQuotation(id);
        
        case 'invoices':
          return await databaseService.deleteInvoice(id);
        
        default:
          throw new Error(`Unknown DELETE endpoint: ${endpoint}`);
      }
    } catch (error) {
      console.error('LocalAPI DELETE error:', error);
      throw this.formatError(error);
    }
  }

  // Format error to match the expected API error format
  formatError(error) {
    return {
      response: {
        data: {
          error: error.message || 'An error occurred'
        },
        status: 500
      },
      message: error.message || 'An error occurred'
    };
  }

  // Helper methods for authentication (simplified for local use)
  async login(credentials) {
    // For standalone mode, we'll use a simple local authentication
    // In a real app, you might want to implement proper local auth
    const { email, password } = credentials;
    
    // Simple validation - in production you'd want proper password hashing
    if (email && password) {
      // Generate a simple token for local use
      const token = `local_token_${Date.now()}`;
      return {
        data: {
          token,
          user: { email }
        }
      };
    } else {
      throw this.formatError(new Error('Invalid credentials'));
    }
  }

  async register(userData) {
    // For standalone mode, we'll accept any registration
    const { email, password } = userData;
    
    if (email && password) {
      return {
        data: {
          message: 'Registration successful'
        }
      };
    } else {
      throw this.formatError(new Error('Email and password are required'));
    }
  }
}

// Create singleton instance
const localApi = new LocalApiService();

// Export with the same interface as the original api
export default {
  get: (endpoint) => localApi.get(endpoint),
  post: (endpoint, data) => localApi.post(endpoint, data),
  put: (endpoint, data) => localApi.put(endpoint, data),
  delete: (endpoint) => localApi.delete(endpoint),
  
  // Auth methods
  login: (credentials) => localApi.login(credentials),
  register: (userData) => localApi.register(userData),
  
  // Interceptors (for compatibility with existing code)
  interceptors: {
    request: {
      use: () => {} // No-op for local API
    },
    response: {
      use: () => {} // No-op for local API
    }
  }
};
